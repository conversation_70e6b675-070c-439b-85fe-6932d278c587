/**
 * 标签页管理器
 */

class TabManager {
    constructor() {
        this.tabs = new Map(); // 存储标签页数据
        this.activeTabId = null;
        this.tabCounter = 0;
        this.maxTabs = 10; // 最大标签页数量
        
        this.tabNav = document.getElementById('tabNav');
        this.tabContentWrapper = document.getElementById('tabContentWrapper');
        this.closeAllBtn = document.getElementById('closeAllTabs');
        
        this.init();
    }
    
    /**
     * 初始化标签页管理器
     */
    init() {
        this.bindEvents();
        this.initializeHomePage();
        this.loadTabsFromStorage();
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 侧边栏链接点击事件
        document.addEventListener('click', (e) => {
            const tabLink = e.target.closest('.tab-link');
            if (tabLink) {
                e.preventDefault();
                this.handleTabLinkClick(tabLink);
            }
        });
        
        // 关闭所有标签页按钮
        if (this.closeAllBtn) {
            this.closeAllBtn.addEventListener('click', () => {
                this.closeAllTabs();
            });
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
        
        // 窗口关闭前保存状态
        window.addEventListener('beforeunload', () => {
            this.saveTabsToStorage();
        });

        // 标签页滚动事件
        if (this.tabNav) {
            this.tabNav.addEventListener('scroll', () => {
                this.updateScrollIndicators();
            });
        }
    }
    
    /**
     * 初始化首页标签页
     */
    initializeHomePage() {
        const homeTab = {
            id: 'home',
            title: '首页',
            icon: 'fas fa-home',
            url: '/',
            isHome: true,
            content: document.getElementById('default-content')
        };
        
        this.tabs.set('home', homeTab);
        this.activeTabId = 'home';
        this.createTabElement(homeTab);
    }
    
    /**
     * 处理标签页链接点击
     */
    handleTabLinkClick(link) {
        const url = link.getAttribute('href');
        const title = link.getAttribute('data-tab-title') || link.textContent.trim();
        const icon = link.getAttribute('data-tab-icon') || 'fas fa-file';
        
        // 检查是否已存在该标签页
        const existingTab = this.findTabByUrl(url);
        if (existingTab) {
            this.switchToTab(existingTab.id);
            return;
        }
        
        // 检查标签页数量限制
        if (this.tabs.size >= this.maxTabs) {
            this.showMessage('最多只能打开 ' + this.maxTabs + ' 个标签页', 'warning');
            return;
        }
        
        // 创建新标签页
        this.createTab(url, title, icon);
    }
    
    /**
     * 创建新标签页
     */
    createTab(url, title, icon) {
        const tabId = 'tab-' + (++this.tabCounter);
        
        const tab = {
            id: tabId,
            title: title,
            icon: icon,
            url: url,
            isHome: false,
            content: null,
            loaded: false
        };
        
        this.tabs.set(tabId, tab);
        this.createTabElement(tab);
        this.switchToTab(tabId);
        this.loadTabContent(tab);
    }
    
    /**
     * 创建标签页DOM元素
     */
    createTabElement(tab) {
        const tabElement = document.createElement('div');
        tabElement.className = 'tab-item';
        tabElement.setAttribute('data-tab-id', tab.id);
        
        if (tab.isHome) {
            tabElement.classList.add('home-tab');
        }
        
        tabElement.innerHTML = `
            <i class="tab-icon ${tab.icon}"></i>
            <span class="tab-title" title="${tab.title}">${tab.title}</span>
            ${!tab.isHome ? '<button class="tab-close" title="关闭标签页"><i class="fas fa-times"></i></button>' : ''}
        `;
        
        // 绑定事件
        tabElement.addEventListener('click', (e) => {
            if (!e.target.closest('.tab-close')) {
                this.switchToTab(tab.id);
            }
        });
        
        if (!tab.isHome) {
            const closeBtn = tabElement.querySelector('.tab-close');
            closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.closeTab(tab.id);
            });
        }
        
        // 添加到导航栏
        this.tabNav.appendChild(tabElement);
        
        // 添加进入动画
        tabElement.classList.add('tab-enter');
        setTimeout(() => {
            tabElement.classList.remove('tab-enter');
        }, 300);
    }
    
    /**
     * 切换到指定标签页
     */
    switchToTab(tabId) {
        if (this.activeTabId === tabId) return;
        
        // 更新标签页状态
        document.querySelectorAll('.tab-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const tabElement = document.querySelector(`[data-tab-id="${tabId}"]`);
        if (tabElement) {
            tabElement.classList.add('active');
        }
        
        // 更新内容区域
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        
        const tab = this.tabs.get(tabId);
        if (tab && tab.content) {
            tab.content.classList.add('active');
        }
        
        this.activeTabId = tabId;
        
        // 更新浏览器历史记录
        if (tab && !tab.isHome) {
            window.history.pushState({ tabId: tabId }, tab.title, tab.url);
        }
        
        // 滚动到可见区域
        this.scrollTabIntoView(tabElement);

        // 更新滚动指示器
        this.updateScrollIndicators();
    }
    
    /**
     * 关闭标签页
     */
    closeTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab || tab.isHome) return;
        
        const tabElement = document.querySelector(`[data-tab-id="${tabId}"]`);
        
        // 添加离开动画
        if (tabElement) {
            tabElement.classList.add('tab-leave');
            setTimeout(() => {
                tabElement.remove();
            }, 300);
        }
        
        // 移除内容
        if (tab.content) {
            tab.content.remove();
        }
        
        // 如果关闭的是当前活动标签页，切换到其他标签页
        if (this.activeTabId === tabId) {
            const remainingTabs = Array.from(this.tabs.keys()).filter(id => id !== tabId);
            if (remainingTabs.length > 0) {
                this.switchToTab(remainingTabs[remainingTabs.length - 1]);
            }
        }
        
        this.tabs.delete(tabId);
    }
    
    /**
     * 关闭所有标签页（除首页外）
     */
    closeAllTabs() {
        const tabsToClose = Array.from(this.tabs.keys()).filter(id => id !== 'home');
        tabsToClose.forEach(tabId => {
            this.closeTab(tabId);
        });
        this.switchToTab('home');
    }
    
    /**
     * 加载标签页内容
     */
    async loadTabContent(tab) {
        if (tab.loaded || tab.isHome) return;
        
        // 创建内容容器
        const contentElement = document.createElement('div');
        contentElement.className = 'tab-pane';
        contentElement.setAttribute('data-tab-id', tab.id);
        contentElement.innerHTML = `
            <div class="tab-loading">
                <div class="spinner-border spinner-border-sm" role="status"></div>
                <span>加载中...</span>
            </div>
        `;
        
        this.tabContentWrapper.appendChild(contentElement);
        tab.content = contentElement;
        
        try {
            // 发送AJAX请求加载内容
            const response = await fetch(tab.url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (response.ok) {
                const html = await response.text();
                contentElement.innerHTML = html;
                tab.loaded = true;
                
                // 执行页面中的脚本
                this.executeScripts(contentElement);
            } else {
                throw new Error('加载失败');
            }
        } catch (error) {
            contentElement.innerHTML = `
                <div class="alert alert-danger">
                    <h5>加载失败</h5>
                    <p>无法加载页面内容，请稍后重试。</p>
                    <button class="btn btn-primary" onclick="tabManager.reloadTab('${tab.id}')">重新加载</button>
                </div>
            `;
        }
    }
    
    /**
     * 执行内容中的脚本
     */
    executeScripts(container) {
        const scripts = container.querySelectorAll('script');
        scripts.forEach(script => {
            const newScript = document.createElement('script');
            if (script.src) {
                newScript.src = script.src;
                newScript.onload = () => {
                    console.log('Script loaded:', script.src);
                };
            } else {
                newScript.textContent = script.textContent;
            }
            document.head.appendChild(newScript);
            // 不要立即移除，让脚本有时间执行
            setTimeout(() => {
                if (newScript.parentNode) {
                    newScript.parentNode.removeChild(newScript);
                }
            }, 100);
        });

        // 处理内联样式
        const styles = container.querySelectorAll('link[rel="stylesheet"]');
        styles.forEach(style => {
            if (!document.querySelector(`link[href="${style.href}"]`)) {
                document.head.appendChild(style.cloneNode(true));
            }
        });
    }
    
    /**
     * 重新加载标签页
     */
    reloadTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (tab && !tab.isHome) {
            tab.loaded = false;
            this.loadTabContent(tab);
        }
    }
    
    /**
     * 根据URL查找标签页
     */
    findTabByUrl(url) {
        for (const tab of this.tabs.values()) {
            if (tab.url === url) {
                return tab;
            }
        }
        return null;
    }
    
    /**
     * 滚动标签页到可见区域
     */
    scrollTabIntoView(tabElement) {
        if (!tabElement) return;
        
        const navRect = this.tabNav.getBoundingClientRect();
        const tabRect = tabElement.getBoundingClientRect();
        
        if (tabRect.left < navRect.left) {
            this.tabNav.scrollLeft -= (navRect.left - tabRect.left + 20);
        } else if (tabRect.right > navRect.right) {
            this.tabNav.scrollLeft += (tabRect.right - navRect.right + 20);
        }
    }
    
    /**
     * 处理键盘快捷键
     */
    handleKeyboardShortcuts(e) {
        // Ctrl+W 关闭当前标签页
        if ((e.ctrlKey || e.metaKey) && e.key === 'w') {
            e.preventDefault();
            if (this.activeTabId !== 'home') {
                this.closeTab(this.activeTabId);
            }
        }
        
        // Ctrl+T 新建标签页（回到首页）
        if ((e.ctrlKey || e.metaKey) && e.key === 't') {
            e.preventDefault();
            this.switchToTab('home');
        }
    }
    
    /**
     * 保存标签页状态到本地存储
     */
    saveTabsToStorage() {
        const tabsData = Array.from(this.tabs.values())
            .filter(tab => !tab.isHome)
            .map(tab => ({
                id: tab.id,
                title: tab.title,
                icon: tab.icon,
                url: tab.url
            }));
        
        localStorage.setItem('nvh_tabs', JSON.stringify({
            tabs: tabsData,
            activeTabId: this.activeTabId,
            counter: this.tabCounter
        }));
    }
    
    /**
     * 从本地存储加载标签页状态
     */
    loadTabsFromStorage() {
        try {
            const data = localStorage.getItem('nvh_tabs');
            if (data) {
                const { tabs, activeTabId, counter } = JSON.parse(data);
                this.tabCounter = counter || 0;
                
                // 恢复标签页
                tabs.forEach(tabData => {
                    const tab = {
                        id: tabData.id,
                        title: tabData.title,
                        icon: tabData.icon,
                        url: tabData.url,
                        isHome: false,
                        content: null,
                        loaded: false
                    };
                    
                    this.tabs.set(tab.id, tab);
                    this.createTabElement(tab);
                });
                
                // 恢复活动标签页
                if (activeTabId && this.tabs.has(activeTabId)) {
                    this.switchToTab(activeTabId);
                }
            }
        } catch (error) {
            console.warn('无法恢复标签页状态:', error);
        }
    }
    
    /**
     * 更新滚动指示器
     */
    updateScrollIndicators() {
        if (!this.tabNav) return;

        const wrapper = this.tabNav.parentElement;
        const canScrollLeft = this.tabNav.scrollLeft > 0;
        const canScrollRight = this.tabNav.scrollLeft < (this.tabNav.scrollWidth - this.tabNav.clientWidth);

        wrapper.classList.toggle('scroll-left', canScrollLeft);
        wrapper.classList.toggle('scroll-right', canScrollRight);
    }

    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info') {
        // 使用现有的消息提示系统
        if (typeof showMessage === 'function') {
            showMessage(message, type);
        } else {
            // 简单的消息提示实现
            this.createToast(message, type);
        }
    }

    /**
     * 创建简单的Toast提示
     */
    createToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        toast.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 1060;
            min-width: 300px;
        `;
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// 全局标签页管理器实例
let tabManager;

// 页面加载完成后初始化标签页管理器
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking for tabNav element...');
    // 只在有用户登录时初始化标签页管理器
    const tabNavElement = document.getElementById('tabNav');
    if (tabNavElement) {
        console.log('TabNav element found, initializing TabManager...');
        tabManager = new TabManager();
        console.log('TabManager initialized successfully');
    } else {
        console.log('TabNav element not found, skipping TabManager initialization');
    }
});
