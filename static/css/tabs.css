/* 标签页样式 */

/* 标签页容器 */
.tab-container {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    position: fixed;
    top: 56px; /* 导航栏高度 */
    left: 16.66667%; /* 默认侧边栏宽度 */
    right: 0;
    z-index: 1020;
    height: 48px;
    transition: all 0.3s ease;
}

/* 标签页导航包装器 */
.tab-nav-wrapper {
    display: flex;
    height: 100%;
    align-items: center;
    padding: 0 15px;
    max-width: 100%;
}

/* 标签页导航区域 */
.tab-nav {
    display: flex;
    flex: 1;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
    gap: 2px;
    padding: 4px 0;
}

.tab-nav::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
}

/* 标签页项目 */
.tab-item {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 6px 6px 0 0;
    padding: 8px 12px;
    margin: 0;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 120px;
    max-width: 200px;
    position: relative;
    font-size: 0.875rem;
    color: #495057;
    text-decoration: none;
}

.tab-item:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
    text-decoration: none;
}

.tab-item.active {
    background-color: #ffffff;
    border-color: #007bff;
    border-bottom-color: #ffffff;
    color: #007bff;
    font-weight: 500;
    z-index: 1;
}

/* 标签页图标 */
.tab-item .tab-icon {
    margin-right: 6px;
    font-size: 0.8rem;
    flex-shrink: 0;
}

/* 标签页标题 */
.tab-item .tab-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 标签页关闭按钮 */
.tab-item .tab-close {
    margin-left: 6px;
    padding: 2px 4px;
    border-radius: 3px;
    background: transparent;
    border: none;
    color: #6c757d;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
    line-height: 1;
}

.tab-item .tab-close:hover {
    background-color: #dc3545;
    color: #ffffff;
}

/* 首页标签页特殊样式（不可关闭） */
.tab-item.home-tab .tab-close {
    display: none;
}

/* 标签页控制按钮 */
.tab-controls {
    margin-left: 10px;
    display: flex;
    gap: 5px;
}

.tab-controls .btn {
    padding: 4px 8px;
    font-size: 0.75rem;
    line-height: 1.2;
}

/* 标签页内容容器 */
.tab-content-container {
    padding-top: 68px !important; /* 导航栏 + 标签页高度 */
    transition: padding-top 0.3s ease;
}

.tab-content-wrapper {
    position: relative;
    min-height: calc(100vh - 124px); /* 全屏高度减去导航栏和标签页 */
}

/* 标签页内容面板 */
.tab-pane {
    display: none;
    width: 100%;
    height: 100%;
}

.tab-pane.active {
    display: block;
}

/* 加载状态 */
.tab-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;
}

.tab-loading .spinner-border {
    margin-right: 10px;
}

/* 侧边栏收起时的调整 */
.sidebar-collapsed .tab-container {
    left: 0;
}

.sidebar-collapsed .tab-content-container {
    margin-left: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* 响应式调整 */
@media (max-width: 767.98px) {
    .tab-container {
        position: relative;
        top: 0;
        height: auto;
        min-height: 48px;
    }
    
    .tab-content-container {
        padding-top: 20px !important;
    }
    
    .tab-nav-wrapper {
        padding: 0 10px;
    }
    
    .tab-item {
        min-width: 100px;
        max-width: 150px;
        font-size: 0.8rem;
        padding: 6px 10px;
    }
    
    .tab-controls .btn {
        padding: 3px 6px;
        font-size: 0.7rem;
    }
}

@media (min-width: 768px) {
    .tab-container {
        left: 25%; /* 侧边栏宽度 */
    }
}

@media (min-width: 992px) {
    .tab-container {
        left: 16.66667%; /* 侧边栏宽度 */
    }
}

/* 标签页拖拽效果 */
.tab-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.tab-item.drag-over {
    border-left: 3px solid #007bff;
}

/* 标签页动画 */
.tab-item.tab-enter {
    animation: tabEnter 0.3s ease;
}

.tab-item.tab-leave {
    animation: tabLeave 0.3s ease;
}

@keyframes tabEnter {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes tabLeave {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

/* 标签页溢出滚动指示器 */
.tab-nav-wrapper::before,
.tab-nav-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    pointer-events: none;
    z-index: 2;
    transition: opacity 0.3s ease;
}

.tab-nav-wrapper::before {
    left: 15px;
    background: linear-gradient(to right, #f8f9fa, transparent);
    opacity: 0;
}

.tab-nav-wrapper::after {
    right: 60px;
    background: linear-gradient(to left, #f8f9fa, transparent);
    opacity: 0;
}

.tab-nav-wrapper.scroll-left::before {
    opacity: 1;
}

.tab-nav-wrapper.scroll-right::after {
    opacity: 1;
}

/* 标签页右键菜单 */
.tab-context-menu {
    position: fixed;
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    min-width: 150px;
    padding: 4px 0;
    font-size: 0.875rem;
}

.tab-context-menu .menu-item {
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
}

.tab-context-menu .menu-item:hover {
    background-color: #f8f9fa;
}

.tab-context-menu .menu-item.disabled {
    color: #6c757d;
    cursor: not-allowed;
}

.tab-context-menu .menu-item.disabled:hover {
    background-color: transparent;
}

.tab-context-menu .menu-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

.tab-context-menu .menu-divider {
    height: 1px;
    background-color: #dee2e6;
    margin: 4px 0;
}
